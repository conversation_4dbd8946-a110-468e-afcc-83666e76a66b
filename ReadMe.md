我想制作一个工具。运行后是一个小窗口，里面的内容就两行，上下两个输入框，每个输入框的后面都有一个输入的按钮，点击输入按钮会弹出地址选择，选择后会在前面的输入框中显示选择的地址。然后右下角有一个确定按钮，点击确定后就开始运行。先打开第一个地址内的HPFirmwareInstaller.exe文件，识别页面上的内容是否与Config.json里面的匹配。对了运行之前先判定当前系统是中文的还是英文的，因为config.json中会有中英两种文本匹配规则。如果都匹配了。那么点击页面的确定，如果不匹配就截图保存一下。当点击确定后会有新的页面，识别页面的内容进行匹配。匹配成功了以后就点击立即更新的选项，然后点击安装的同时进行计时，当页面更新完毕以后会变化，判断页面变化后停止计时并且把时间写在Time.txt文件内。页面有几个元素，第一个是包版本，第二个是正要安装的包版本。使用pyside6和pywinauto来写，所有的元素都要有圆角。圆角给6吧。再想到其他的再补充。你给我出一个计划流程。